## Model Configuration
model:
  base:
    N: 6                    # Number of layers
    d_model: 512            # Model dimension
    d_ff: 2048              # Feed-forward dimension
    h: 8                    # Number of attention heads
    d_k: 64                 # Key dimension
    d_v: 64                 # Value dimension
    dropout: 0.1            # Dropout rate
    label_smoothing: 0.1    # Label smoothing value
  big:
    N: 6                    # Number of layers
    d_model: 1024           # Model dimension
    d_ff: 4096              # Feed-forward dimension
    h: 16                   # Number of attention heads
    d_k: 64                 # Key dimension
    d_v: 64                 # Value dimension
    dropout: 0.3            # Dropout rate
    label_smoothing: 0.1    # Label smoothing value

## Training Configuration
training:
  warmup_steps: 4000        # Warmup steps for learning rate scheduling
  batch_size_source: 25000  # Approximate number of source tokens per batch
  batch_size_target: 25000  # Approximate number of target tokens per batch
  max_epochs: 100000        # Maximum training steps (100K for base model)
  max_epochs_big: 300000    # Maximum training steps for big model (300K)
  checkpoint_interval: 6000 # Checkpoint saving interval (10 minutes approximate)

## Optimizer Configuration
optimizer:
  beta1: 0.9               # Adam optimizer beta1
  beta2: 0.98              # Adam optimizer beta2
  epsilon: 1e-9            # Adam optimizer epsilon

## Inference Configuration
inference:
  beam_size: 4             # Beam search size
  length_penalty: 0.6      # Length penalty alpha for beam search
  max_output_length: 50    # Maximum output length relative to input

## Dataset Configuration
dataset:
  en_de:
    name: "WMT 2014 English-German"
    sentence_pairs: 4500000  # Approximately 4.5 million sentence pairs
    vocab_type: "byte-pair"  # Byte-pair encoding
    vocab_size: 37000        # Shared source-target vocabulary
  en_fr:
    name: "WMT 2014 English-French"
    sentence_pairs: 36000000 # 36 million sentences
    vocab_type: "word-piece" # Word-piece vocabulary
    vocab_size: 32000        # Vocabulary size

## Hardware Configuration
hardware:
  gpus: 8                  # Number of GPUs used
  gpu_type: "NVIDIA P100"  # GPU model

## Evaluation Configuration
evaluation:
  base_model_checkpoints: 5   # Number of last checkpoints to average for base model
  big_model_checkpoints: 20   # Number of last checkpoints to average for big model