```python
## main.py
import argparse
import os
import torch
from torch.utils.data import DataLoader
import pytorch_lightning as pl
from pytorch_lightning.callbacks import ModelCheckpoint
from pytorch_lightning.loggers import TensorBoardLogger

from config import Config
from data.tokenizer import TokenizerWrapper
from data.dataset import TranslationDataset, create_data_loader
from model.transformer import Transformer
from training.trainer import TransformerTrainer
from evaluation.evaluator import Evaluator


def parse_args():
    """
    Parse command line arguments.
    
    Returns:
        argparse.Namespace: Parsed arguments
    """
    parser = argparse.ArgumentParser(description="Transformer Model Training and Evaluation")
    
    parser.add_argument(
        "--mode",
        type=str,
        default="train",
        choices=["train", "evaluate"],
        help="Mode to run: train or evaluate"
    )
    
    parser.add_argument(
        "--config",
        type=str,
        default="config.yaml",
        help="Path to the configuration file"
    )
    
    parser.add_argument(
        "--model_type",
        type=str,
        default="base",
        choices=["base", "big"],
        help="Model type: base or big"
    )
    
    parser.add_argument(
        "--checkpoint_path",
        type=str,
        default=None,
        help="Path to a specific checkpoint for evaluation"
    )
    
    parser.add_argument(
        "--data_dir",
        type=str,
        default="./data",
        help="Directory containing the dataset files"
    )
    
    parser.add_argument(
        "--dataset_type",
        type=str,
        default="en_de",
        choices=["en_de", "en_fr"],
        help="Dataset type: en_de (English-German) or en_fr (English-French)"
    )
    
    return parser.parse_args()


def setup_tokenizer(config: Config, dataset_type: str, data_dir: str) -> TokenizerWrapper:
    """
    Set up the tokenizer based on configuration.
    
    Args:
        config (Config): Configuration object
        dataset_type (str): Type of dataset (en_de or en_fr)
        data_dir (str): Directory containing data files
        
    Returns:
        TokenizerWrapper: Initialized tokenizer
    """
    dataset_config = config.get_dataset_config(dataset_type)
    vocab_type = dataset_config["vocab_type"]
    vocab_size = dataset_config["vocab_size"]
    
    # Define vocabulary file path
    vocab_file = os.path.join(data_dir, f"{dataset_type}_vocab.json")
    
    # Initialize tokenizer
    tokenizer = TokenizerWrapper(
        vocab_path=vocab_file if os.path.exists(vocab_file) else None,
        vocab_type=vocab_type,
        vocab_size=vocab_size
    )
    
    # If vocabulary doesn't exist, train tokenizer
    if not os.path.exists(vocab_file):
        print("Training tokenizer...")
        src_file = os.path.join(data_dir, f"{dataset_type}_train_src.txt")
        tgt_file = os.path.join(data_dir, f"{dataset_type}_train_tgt.txt")
        
        if os.path.exists(src_file) and os.path.exists(tgt_file):
            tokenizer.train([src_file, tgt_file])
            tokenizer.save_vocab(vocab_file)
            print(f"Vocabulary saved to {vocab_file}")
        else:
            raise FileNotFoundError(f"Training files not found: {src_file} or {tgt_file}")
    
    return tokenizer


def setup_datasets(config: Config, tokenizer: TokenizerWrapper, data_dir: str, dataset_type: str) -> tuple:
    """
    Set up training, validation, and test datasets.
    
    Args:
        config (Config): Configuration object
        tokenizer (TokenizerWrapper): Tokenizer instance
        data_dir (str): Directory containing data files
        dataset_type (str): Type of dataset (en_de or en_fr)
        
    Returns:
        tuple: (train_dataset, val_dataset, test_dataset)
    """
    # Get training configuration
    training_config = config.get_training_config()
    
    # Define file paths
    train_src = os.path.join(data_dir, f"{dataset_type}_train_src.txt")
    train_tgt = os.path.join(data_dir, f"{dataset_type}_train_tgt.txt")
    val_src = os.path.join(data_dir, f"{dataset_type}_val_src.txt")
    val_tgt = os.path.join(data_dir, f"{dataset_type}_val_tgt.txt")
    test_src = os.path.join(data_dir, f"{dataset_type}_test_src.txt")
    test_tgt = os.path.join(data_dir, f"{dataset_type}_test_tgt.txt")
    
    # Check if files exist
    required_files = [train_src, train_tgt, val_src, val_tgt, test_src, test_tgt]
    for file_path in required_files:
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Required data file not found: {file_path}")
    
    # Create datasets
    train_dataset = TranslationDataset(
        source_path=train_src,
        target_path=train_tgt,
        tokenizer=tokenizer,
        batch_size_source=training_config["batch_size_source"],
        batch_size_target=training_config["batch_size_target"]
    )
    
    val_dataset = TranslationDataset(
        source_path=val_src,
        target_path=val_tgt,
        tokenizer=tokenizer,
        batch_size_source=training_config["batch_size_source"],
        batch_size_target=training_config["batch_size_target"]
    )
    
    test_dataset = TranslationDataset(
        source_path=test_src,
        target_path=test_tgt,
        tokenizer=tokenizer,
        batch_size_source=training_config["batch_size_source"],
        batch_size_target=training_config["batch_size_target"]
    )
    
    return train_dataset, val_dataset, test_dataset


def train_model(config: Config, model_type: str, train_dataset: TranslationDataset, 
                val_dataset: TranslationDataset, checkpoint_path: str = None):
    """
    Train the Transformer model.
    
    Args:
        config (Config): Configuration object
        model_type (str): Type of model (base or big)
        train_dataset (TranslationDataset): Training dataset
        val_dataset (TranslationDataset): Validation dataset
        checkpoint_path (str): Path to a checkpoint to resume training
    """
    # Get model configuration
    model_config = config.get_model_config(model_type)
    
    # Create data loaders
    train_loader = create_data_loader(train_dataset, batch_size=1, shuffle=True)
    val_loader = create_data_loader(val_dataset, batch_size=1, shuffle=False)
    
    # Initialize model trainer
    model_trainer = TransformerTrainer(
        src_vocab_size=train_dataset.tokenizer.get_vocab_size(),
        tgt_vocab_size=train_dataset.tokenizer.get_vocab_size(),
        src_pad_idx=0,
        tgt_pad_idx=0,
        model_type=model_type
    )
    
    # Load checkpoint if provided
    if checkpoint_path and os.path.exists(checkpoint_path):
        model_trainer.load_state_dict(torch.load(checkpoint_path)["state_dict"])
        print(f"Loaded checkpoint from {checkpoint_path}")
    
    # Set up logging
    logger = TensorBoardLogger("tb_logs", name=f"transformer_{model_type}")
    
    # Set up checkpointing
    checkpoint_callback = ModelCheckpoint(
        dirpath=f"checkpoints/{model_type}",
        filename="{epoch}-{val_loss:.2f}",
        save_top_k=3,
        monitor="val_loss",
        mode="min"
    )
    
    # Get training configuration
    training_config = config.get_training_config()
    max_epochs = training_config["max_epochs"] if model_type == "base" else training_config["max_epochs_big"]
    
    # Initialize PyTorch Lightning trainer
    trainer = pl.Trainer(
        max_epochs=max_epochs,
        logger=logger,
        callbacks=[checkpoint_callback],
        gpus=1 if torch.cuda.is_available() else 0,
        precision=16 if torch.cuda.is_available() else 32,  # Use mixed precision if GPU is available
        gradient_clip_val=1.0,  # Gradient clipping as commonly used in Transformer implementations
    )
    
    # Start training
    trainer.fit(model_trainer, train_loader, val_loader)


def evaluate_model(config: Config, model_type: str, test_dataset: TranslationDataset, 
                   tokenizer: TokenizerWrapper, checkpoint_path: str = None):
    """
    Evaluate the Transformer model.
    
    Args:
        config (Config): Configuration object
        model_type (str): Type of model (base or big)
        test_dataset (TranslationDataset): Test dataset
        tokenizer (TokenizerWrapper): Tokenizer instance
        checkpoint_path (str): Path to a checkpoint for evaluation
    """
    # Check if checkpoint path is provided
    if not checkpoint_path:
        raise ValueError("Checkpoint path must be provided for evaluation")
    
    if not os.path.exists(checkpoint_path):
        raise FileNotFoundError(f"Checkpoint file not found: {checkpoint_path}")
    
    # Get model configuration
    model_config = config.get_model_config(model_type)
    
    # Initialize model
    model = Transformer(
        config=config,
        src_vocab_size=tokenizer.get_vocab_size(),
        tgt_vocab_size=tokenizer.get_vocab_size(),
        src_pad_idx=0,
        tgt_pad_idx=0
    )
    
    # Load checkpoint
    checkpoint = torch.load(checkpoint_path)
    model.load_state_dict(checkpoint["state_dict"])
    print(f"Loaded checkpoint from {checkpoint_path}")
    
    # Initialize evaluator
    evaluator = Evaluator(
        model=model,
        test_dataset=test_dataset,
        tokenizer=tokenizer,
        config=config
    )
    
    # Run evaluation
    print("Running evaluation...")
    metrics = evaluator.evaluate()
    
    # Print results
    print("\n=== Evaluation Results ===")
    print(f"BLEU Score: {metrics['bleu_score']:.2f}")
    print(f"1-gram Precision: {metrics['bleu_details']['1-gram']:.2f}")
    print(f"2-gram Precision: {metrics['bleu_details']['2-gram']:.2f}")
    print(f"3-gram Precision: {metrics['bleu_details']['3-gram']:.2f}")
    print(f"4-gram Precision: {metrics['bleu_details']['4-gram']:.2f}")
    print(f"Brevity Penalty: {metrics['bleu_details']['brevity_penalty']:.4f}")
    print(f"Length Ratio: {metrics['bleu_details']['length_ratio']:.4f}")


def main():
    """
    Main function to run the Transformer model training or evaluation.
    """
    # Parse command line arguments
    args = parse_args()
    
    # Load configuration
    config = Config(args.config)
    
    # Set up tokenizer
    print("Setting up tokenizer...")
    tokenizer = setup_tokenizer(config, args.dataset_type, args.data_dir)
    print(f"Vocabulary size: {tokenizer.get_vocab_size()}")
    
    # Set up datasets
    print("Setting up datasets...")
    train_dataset, val_dataset, test_dataset = setup_datasets(
        config, tokenizer, args.data_dir, args.dataset_type
    )
    print(f"Training samples: {len(train_dataset)}")
    print(f"Validation samples: {len(val_dataset)}")
    print(f"Test samples: {len(test_dataset)}")
    
    # Run selected mode
    if args.mode == "train":
        print("Starting training...")
        train_model(
            config=config,
            model_type=args.model_type,
            train_dataset=train_dataset,
            val_dataset=val_dataset,
            checkpoint_path=args.checkpoint_path
        )
    elif args.mode == "evaluate":
        print("Starting evaluation...")
        evaluate_model(
            config=config,
            model_type=args.model_type,
            test_dataset=test_dataset,
            tokenizer=tokenizer,
            checkpoint_path=args.checkpoint_path
        )
    else:
        raise ValueError(f"Invalid mode: {args.mode}")


if __name__ == "__main__":
    main()

```