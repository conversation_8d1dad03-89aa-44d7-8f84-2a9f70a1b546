```python
## data/dataset.py
import torch
from torch.utils.data import Dataset, DataLoader
from typing import List, Dict, Tuple, Any, Iterator
from data.tokenizer import TokenizerWrapper
import math


class TranslationDataset(Dataset):
    """
    Dataset class for handling parallel text data for machine translation.
    Loads source and target text files, applies tokenization, and prepares
    batches with proper padding and attention masks.
    """
    
    def __init__(self, 
                 source_path: str, 
                 target_path: str, 
                 tokenizer: TokenizerWrapper, 
                 max_length: int = 512,
                 batch_size_source: int = 25000,
                 batch_size_target: int = 25000):
        """
        Initialize the dataset with source and target text files.
        
        Args:
            source_path (str): Path to the source language text file
            target_path (str): Path to the target language text file
            tokenizer (TokenizerWrapper): Tokenizer instance for encoding text
            max_length (int): Maximum sequence length
            batch_size_source (int): Approximate number of source tokens per batch
            batch_size_target (int): Approximate number of target tokens per batch
        """
        self.source_path = source_path
        self.target_path = target_path
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.batch_size_source = batch_size_source
        self.batch_size_target = batch_size_target
        
        # Load data
        self.source_sentences = self._load_sentences(source_path)
        self.target_sentences = self._load_sentences(target_path)
        
        # Validate data
        if len(self.source_sentences) != len(self.target_sentences):
            raise ValueError("Source and target files must have the same number of lines")
        
        # Tokenize all sentences
        self.source_tokens = [self.tokenizer.encode(sent) for sent in self.source_sentences]
        self.target_tokens = [self.tokenizer.encode(sent) for sent in self.target_sentences]
        
        # Create batches
        self.batches = self._create_batches()
    
    def _load_sentences(self, path: str) -> List[str]:
        """
        Load sentences from a text file.
        
        Args:
            path (str): Path to the text file
            
        Returns:
            List[str]: List of sentences
        """
        with open(path, 'r', encoding='utf-8') as file:
            sentences = [line.strip() for line in file if line.strip()]
        return sentences
    
    def _create_batches(self) -> List[Tuple[List[List[int]], List[List[int]]]]:
        """
        Create batches of sentences grouped by similar lengths for efficient padding.
        
        Returns:
            List[Tuple[List[List[int]], List[List[int]]]]: List of batches, each containing
                                                          source and target token sequences
        """
        # Create list of (source_tokens, target_tokens, source_length, target_length)
        token_pairs = [(self.source_tokens[i], self.target_tokens[i], 
                        len(self.source_tokens[i]), len(self.target_tokens[i]))
                       for i in range(len(self.source_tokens))]
        
        # Sort by source length then target length
        token_pairs.sort(key=lambda x: (x[2], x[3]))
        
        batches = []
        current_batch_source = []
        current_batch_target = []
        current_source_tokens = 0
        current_target_tokens = 0
        
        for src_tokens, tgt_tokens, src_len, tgt_len in token_pairs:
            # Truncate sequences that are too long
            if src_len > self.max_length:
                src_tokens = src_tokens[:self.max_length]
                src_len = self.max_length
                
            if tgt_len > self.max_length:
                tgt_tokens = tgt_tokens[:self.max_length]
                tgt_len = self.max_length
            
            # Check if adding this sentence would exceed batch limits
            new_source_tokens = current_source_tokens + src_len
            new_target_tokens = current_target_tokens + tgt_len
            
            # If adding this sentence exceeds limits, finalize current batch and start new one
            if (new_source_tokens > self.batch_size_source or 
                new_target_tokens > self.batch_size_target or
                len(current_batch_source) >= 1024):  # Hard limit to prevent very large batches
                
                if current_batch_source:  # Only add non-empty batches
                    batches.append((current_batch_source, current_batch_target))
                
                # Start new batch with current sentence
                current_batch_source = [src_tokens]
                current_batch_target = [tgt_tokens]
                current_source_tokens = src_len
                current_target_tokens = tgt_len
            else:
                # Add to current batch
                current_batch_source.append(src_tokens)
                current_batch_target.append(tgt_tokens)
                current_source_tokens = new_source_tokens
                current_target_tokens = new_target_tokens
        
        # Add last batch if not empty
        if current_batch_source:
            batches.append((current_batch_source, current_batch_target))
            
        return batches
    
    def __len__(self) -> int:
        """
        Get the number of batches in the dataset.
        
        Returns:
            int: Number of batches
        """
        return len(self.batches)
    
    def __getitem__(self, idx: int) -> Dict[str, Any]:
        """
        Get a batch by index.
        
        Args:
            idx (int): Index of the batch
            
        Returns:
            Dict[str, Any]: Dictionary containing source and target tokens with metadata
        """
        source_batch, target_batch = self.batches[idx]
        return {
            'source_tokens': source_batch,
            'target_tokens': target_batch
        }
    
    @staticmethod
    def collate_fn(batch: List[Dict[str, Any]]) -> Dict[str, torch.Tensor]:
        """
        Collate function to process a batch of data into tensors with proper padding.
        
        Args:
            batch (List[Dict[str, Any]]): List of items from __getitem__
            
        Returns:
            Dict[str, torch.Tensor]: Batched and padded tensors with attention masks
        """
        # Extract source and target sequences
        source_batch = [item['source_tokens'][0] for item in batch]  # Unpack single-item lists
        target_batch = [item['target_tokens'][0] for item in batch]
        
        # Get batch size and max lengths
        batch_size = len(source_batch)
        max_src_len = max(len(seq) for seq in source_batch)
        max_tgt_len = max(len(seq) for seq in target_batch)
        
        # Pad sequences
        padded_sources = torch.full((batch_size, max_src_len), 
                                   fill_value=0, dtype=torch.long)  # 0 for PAD token
        padded_targets = torch.full((batch_size, max_tgt_len), 
                                   fill_value=0, dtype=torch.long)
        
        # Create attention masks
        src_masks = torch.ones((batch_size, 1, 1, max_src_len), dtype=torch.bool)
        tgt_masks = torch.ones((batch_size, 1, max_tgt_len, max_tgt_len), dtype=torch.bool)
        
        for i, (src_seq, tgt_seq) in enumerate(zip(source_batch, target_batch)):
            src_len = len(src_seq)
            tgt_len = len(tgt_seq)
            
            # Copy sequences
            padded_sources[i, :src_len] = torch.tensor(src_seq, dtype=torch.long)
            padded_targets[i, :tgt_len] = torch.tensor(tgt_seq, dtype=torch.long)
            
            # Create source mask (mask padding tokens)
            src_masks[i, 0, 0, :src_len] = False  # False means "attend to this position"
            
            # Create target mask (causal mask + padding mask)
            # Padding mask
            tgt_masks[i, 0, :tgt_len, :tgt_len] = False
            
            # Causal mask (upper triangular matrix for causal attention)
            tgt_masks[i, 0] = tgt_masks[i, 0].logical_or(torch.triu(torch.ones(tgt_len, tgt_len, dtype=torch.bool), diagonal=1))
        
        # Prepare decoder input and target (shifted by one position)
        decoder_input = padded_targets[:, :-1]  # Remove last token
        decoder_target = padded_targets[:, 1:]  # Remove first token (BOS)
        
        # Adjust target mask for decoder input length
        tgt_masks = tgt_masks[:, :, :-1, :-1]  # Remove last row and column
        
        return {
            'src_tokens': padded_sources,
            'tgt_tokens': decoder_input,
            'tgt_masks': tgt_masks,
            'src_masks': src_masks,
            'targets': decoder_target
        }


def create_data_loader(dataset: TranslationDataset, 
                      batch_size: int = 1, 
                      shuffle: bool = False, 
                      num_workers: int = 0) -> DataLoader:
    """
    Create a DataLoader for the translation dataset.
    
    Args:
        dataset (TranslationDataset): The dataset to load
        batch_size (int): Batch size (typically 1 since we pre-batch)
        shuffle (bool): Whether to shuffle the data
        num_workers (int): Number of worker processes
        
    Returns:
        DataLoader: PyTorch DataLoader instance
    """
    return DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers,
        collate_fn=TranslationDataset.collate_fn,
        pin_memory=True
    )

```