```python
## model/embedding.py
import torch
import torch.nn as nn
import math
from typing import Tuple


class EmbeddingLayer(nn.Module):
    """
    Embedding layer that combines token embeddings with positional encodings.
    Implements the embedding and positional encoding as described in Sections 3.4 and 3.5
    of the "Attention Is All You Need" paper.
    """
    
    def __init__(self, vocab_size: int, d_model: int, max_len: int = 5000, dropout: float = 0.1):
        """
        Initialize the EmbeddingLayer.
        
        Args:
            vocab_size (int): Size of the vocabulary
            d_model (int): Dimension of the model embeddings
            max_len (int): Maximum sequence length for positional encodings
            dropout (float): Dropout rate to apply to the sum of embeddings and positional encodings
        """
        super(EmbeddingLayer, self).__init__()
        self.d_model = d_model
        self.dropout = nn.Dropout(p=dropout)
        
        # Token embedding layer
        self.token_embedding = nn.Embedding(vocab_size, d_model)
        
        # Create positional encodings
        self.register_buffer('positional_encoding', self._create positional_encoding(max_len, d_model))
    
    def _create_positional_encoding(self, max_len: int, d_model: int) -> torch.Tensor:
        """
        Create sinusoidal positional encodings as described in Section 3.5 of the paper.
        
        Args:
            max_len (int): Maximum sequence length
            d_model (int): Dimension of the model
            
        Returns:
            torch.Tensor: Positional encodings of shape (1, max_len, d_model)
        """
        # Create a matrix of shape (max_len, d_model) for positional encodings
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        
        # Calculate the div_term for scaling
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                            (-math.log(10000.0) / d_model))
        
        # Apply sin to even indices (0, 2, 4, ...)
        pe[:, 0::2] = torch.sin(position * div_term)
        # Apply cos to odd indices (1, 3, 5, ...)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        # Add batch dimension and return
        return pe.unsqueeze(0)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through the embedding layer.
        
        Args:
            x (torch.Tensor): Input tensor of token IDs with shape (batch_size, seq_len)
            
        Returns:
            torch.Tensor: Embedded sequences with shape (batch_size, seq_len, d_model)
        """
        # Get token embeddings and scale by sqrt(d_model) as described in Section 3.4
        token_embeddings = self.token_embedding(x) * math.sqrt(self.d_model)
        
        # Add positional encodings
        seq_len = x.size(1)
        positional_encodings = self.positional_encoding[:, :seq_len]
        embeddings = token_embeddings + positional_encodings
        
        # Apply dropout
        return self.dropout(embeddings)

```