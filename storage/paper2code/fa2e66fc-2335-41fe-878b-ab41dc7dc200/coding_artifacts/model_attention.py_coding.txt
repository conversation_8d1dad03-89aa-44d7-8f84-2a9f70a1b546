```python
## model/attention.py
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Optional, <PERSON>ple
from torch import Tensor


def scaled_dot_product_attention(query: Tensor, key: Tensor, value: Tensor, 
                                mask: Optional[Tensor] = None, 
                                dropout: Optional[nn.Dropout] = None) -> Tuple[Tensor, Tensor]:
    """
    Compute scaled dot-product attention as described in Section 3.2.1 of the paper.
    
    Args:
        query (Tensor): Query tensor of shape (batch_size, num_heads, seq_len, d_k)
        key (Tensor): Key tensor of shape (batch_size, num_heads, seq_len, d_k)
        value (Tensor): Value tensor of shape (batch_size, num_heads, seq_len, d_v)
        mask (Optional[Tensor]): Mask tensor to prevent attention to certain positions
        dropout (Optional[nn.Dropout]): Dropout layer to apply to attention weights
        
    Returns:
        Tuple[Tensor, Tensor]: Output tensor and attention weights
    """
    # Compute attention scores: QK^T / sqrt(d_k)
    d_k = query.size(-1)
    scores = torch.matmul(query, key.transpose(-2, -1)) / math.sqrt(d_k)
    
    # Apply mask if provided (set masked positions to -1e9)
    if mask is not None:
        scores = scores.masked_fill(mask == 1, -1e9)  # Mask value 1 means "ignore this position"
    
    # Apply softmax to get attention weights
    attention_weights = F.softmax(scores, dim=-1)
    
    # Apply dropout if provided
    if dropout is not None:
        attention_weights = dropout(attention_weights)
    
    # Multiply weights with values to get output
    output = torch.matmul(attention_weights, value)
    
    return output, attention_weights


class MultiHeadAttention(nn.Module):
    """
    Multi-head attention mechanism as described in Section 3.2.2 of the paper.
    Allows the model to jointly attend to information from different representation
    subspaces at different positions.
    """
    
    def __init__(self, d_model: int, num_heads: int, dropout: float = 0.1):
        """
        Initialize the MultiHeadAttention module.
        
        Args:
            d_model (int): Dimension of the model (512 for base, 1024 for big)
            num_heads (int): Number of attention heads (8 for base, 16 for big)
            dropout (float): Dropout rate for attention weights
        """
        super(MultiHeadAttention, self).__init__()
        assert d_model % num_heads == 0, "d_model must be divisible by num_heads"
        
        # Dimension of keys, values, and queries for each head
        self.d_k = d_model // num_heads
        self.d_v = d_model // num_heads
        self.num_heads = num_heads
        self.d_model = d_model
        
        # Linear projections for queries, keys, and values
        self.query_projection = nn.Linear(d_model, d_model)
        self.key_projection = nn.Linear(d_model, d_model)
        self.value_projection = nn.Linear(d_model, d_model)
        
        # Output projection
        self.output_projection = nn.Linear(d_model, d_model)
        
        # Dropout for attention weights
        self.attention_dropout = nn.Dropout(dropout)
        
    def forward(self, query: Tensor, key: Tensor, value: Tensor, 
                mask: Optional[Tensor] = None) -> Tuple[Tensor, Tensor]:
        """
        Forward pass through the multi-head attention mechanism.
        
        Args:
            query (Tensor): Query tensor of shape (batch_size, seq_len, d_model)
            key (Tensor): Key tensor of shape (batch_size, seq_len, d_model)
            value (Tensor): Value tensor of shape (batch_size, seq_len, d_model)
            mask (Optional[Tensor]): Mask tensor to prevent attention to certain positions
            
        Returns:
            Tuple[Tensor, Tensor]: Output tensor and attention weights
        """
        batch_size = query.size(0)
        
        # Apply linear projections to queries, keys, and values
        # Shape: (batch_size, seq_len, d_model)
        query = self.query_projection(query)
        key = self.key_projection(key)
        value = self.value_projection(value)
        
        # Reshape and transpose for multi-head attention
        # Shape: (batch_size, num_heads, seq_len, d_k)
        query = query.view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        key = key.view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        value = value.view(batch_size, -1, self.num_heads, self.d_v).transpose(1, 2)
        
        # Apply scaled dot-product attention
        # Output shape: (batch_size, num_heads, seq_len, d_v)
        # Attention weights shape: (batch_size, num_heads, seq_len, seq_len)
        output, attention_weights = scaled_dot_product_attention(
            query, key, value, mask, self.attention_dropout
        )
        
        # Concatenate heads
        # Transpose to (batch_size, seq_len, num_heads, d_v)
        # Then reshape to (batch_size, seq_len, d_model)
        output = output.transpose(1, 2).contiguous().view(batch_size, -1, self.d_model)
        
        # Apply output projection
        output = self.output_projection(output)
        
        return output, attention_weights

```