## model/transformer.py
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple
from model.embedding import EmbeddingLayer
from model.attention import MultiHeadAttention
import math


class PositionwiseFeedForward(nn.Module):
    """
    Position-wise Feed-Forward Networks as described in Section 3.3 of the paper.
    This is a fully connected feed-forward network applied to each position separately and identically.
    """
    
    def __init__(self, d_model: int, d_ff: int, dropout: float = 0.1):
        """
        Initialize the PositionwiseFeedForward module.
        
        Args:
            d_model (int): Dimension of the model (512 for base, 1024 for big)
            d_ff (int): Dimension of the inner-layer (2048 for base, 4096 for big)
            dropout (float): Dropout rate
        """
        super(PositionwiseFeedForward, self).__init__()
        self.w_1 = nn.Linear(d_model, d_ff)
        self.w_2 = nn.Linear(d_ff, d_model)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through the position-wise feed-forward network.
        
        Args:
            x (torch.Tensor): Input tensor of shape (batch_size, seq_len, d_model)
            
        Returns:
            torch.Tensor: Output tensor of shape (batch_size, seq_len, d_model)
        """
        return self.w_2(self.dropout(F.relu(self.w_1(x))))


class LayerNorm(nn.Module):
    """
    Layer Normalization as described in the paper.
    This implementation follows the approach mentioned in the paper.
    """
    
    def __init__(self, features: int, eps: float = 1e-6):
        """
        Initialize the LayerNorm module.
        
        Args:
            features (int): Number of features (d_model)
            eps (float): Epsilon value to prevent division by zero
        """
        super(LayerNorm, self).__init__()
        self.a_2 = nn.Parameter(torch.ones(features))
        self.b_2 = nn.Parameter(torch.zeros(features))
        self.eps = eps

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Apply layer normalization to the input tensor.
        
        Args:
            x (torch.Tensor): Input tensor of shape (batch_size, seq_len, d_model)
            
        Returns:
            torch.Tensor: Normalized tensor of same shape
        """
        mean = x.mean(-1, keepdim=True)
        std = x.std(-1, keepdim=True)
        return self.a_2 * (x - mean) / (std + self.eps) + self.b_2


class EncoderLayer(nn.Module):
    """
    Single encoder layer as described in Section 3.1 of the paper.
    Each layer has two sub-layers:
    1. Multi-head self-attention mechanism
    2. Position-wise fully connected feed-forward network
    """
    
    def __init__(self, d_model: int, num_heads: int, d_ff: int, dropout: float = 0.1):
        """
        Initialize the EncoderLayer.
        
        Args:
            d_model (int): Dimension of the model
            num_heads (int): Number of attention heads
            d_ff (int): Dimension of the feed-forward network
            dropout (float): Dropout rate
        """
        super(EncoderLayer, self).__init__()
        self.self_attention = MultiHeadAttention(d_model, num_heads, dropout)
        self.feed_forward = PositionwiseFeedForward(d_model, d_ff, dropout)
        self.layer_norm1 = LayerNorm(d_model)
        self.layer_norm2 = LayerNorm(d_model)
        self.dropout1 = nn.Dropout(dropout)
        self.dropout2 = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Forward pass through the encoder layer.
        
        Args:
            x (torch.Tensor): Input tensor of shape (batch_size, seq_len, d_model)
            mask (Optional[torch.Tensor]): Mask tensor for attention
            
        Returns:
            torch.Tensor: Output tensor of shape (batch_size, seq_len, d_model)
        """
        # Multi-head self-attention sub-layer with residual connection and layer normalization
        attn_output, _ = self.self_attention(x, x, x, mask)
        x = self.layer_norm1(x + self.dropout1(attn_output))
        
        # Position-wise feed-forward sub-layer with residual connection and layer normalization
        ff_output = self.feed_forward(x)
        x = self.layer_norm2(x + self.dropout2(ff_output))
        
        return x


class DecoderLayer(nn.Module):
    """
    Single decoder layer as described in Section 3.1 of the paper.
    Each layer has three sub-layers:
    1. Masked multi-head self-attention mechanism
    2. Multi-head attention over encoder output
    3. Position-wise fully connected feed-forward network
    """
    
    def __init__(self, d_model: int, num_heads: int, d_ff: int, dropout: float = 0.1):
        """
        Initialize the DecoderLayer.
        
        Args:
            d_model (int): Dimension of the model
            num_heads (int): Number of attention heads
            d_ff (int): Dimension of the feed-forward network
            dropout (float): Dropout rate
        """
        super(DecoderLayer, self).__init__()
        self.self_attention = MultiHeadAttention(d_model, num_heads, dropout)
        self.encoder_attention = MultiHeadAttention(d_model, num_heads, dropout)
        self.feed_forward = PositionwiseFeedForward(d_model, d_ff, dropout)
        self.layer_norm1 = LayerNorm(d_model)
        self.layer_norm2 = LayerNorm(d_model)
        self.layer_norm3 = LayerNorm(d_model)
        self.dropout1 = nn.Dropout(dropout)
        self.dropout2 = nn.Dropout(dropout)
        self.dropout3 = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor, 
                encoder_output: torch.Tensor,
                src_mask: Optional[torch.Tensor] = None,
                tgt_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Forward pass through the decoder layer.
        
        Args:
            x (torch.Tensor): Input tensor of shape (batch_size, tgt_seq_len, d_model)
            encoder_output (torch.Tensor): Encoder output of shape (batch_size, src_seq_len, d_model)
            src_mask (Optional[torch.Tensor]): Source mask for encoder-decoder attention
            tgt_mask (Optional[torch.Tensor]): Target mask for self-attention (causal masking)
            
        Returns:
            torch.Tensor: Output tensor of shape (batch_size, tgt_seq_len, d_model)
        """
        # Masked multi-head self-attention sub-layer with residual connection and layer normalization
        attn_output1, _ = self.self_attention(x, x, x, tgt_mask)
        x = self.layer_norm1(x + self.dropout1(attn_output1))
        
        # Multi-head attention over encoder output with residual connection and layer normalization
        attn_output2, _ = self.encoder_attention(x, encoder_output, encoder_output, src_mask)
        x = self.layer_norm2(x + self.dropout2(attn_output2))
        
        # Position-wise feed-forward sub-layer with residual connection and layer normalization
        ff_output = self.feed_forward(x)
        x = self.layer_norm3(x + self.dropout3(ff_output))
        
        return x


class Encoder(nn.Module):
    """
    Encoder stack as described in Section 3.1 of the paper.
    The encoder is composed of a stack of N = 6 identical layers.
    """
    
    def __init__(self, num_layers: int, d_model: int, num_heads: int, d_ff: int, dropout: float = 0.1):
        """
        Initialize the Encoder.
        
        Args:
            num_layers (int): Number of encoder layers (N=6)
            d_model (int): Dimension of the model
            num_heads (int): Number of attention heads
            d_ff (int): Dimension of the feed-forward network
            dropout (float): Dropout rate
        """
        super(Encoder, self).__init__()
        self.layers = nn.ModuleList([
            EncoderLayer(d_model, num_heads, d_ff, dropout) for _ in range(num_layers)
        ])
        self.layer_norm = LayerNorm(d_model)
        
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Forward pass through the encoder stack.
        
        Args:
            x (torch.Tensor): Input tensor of shape (batch_size, seq_len, d_model)
            mask (Optional[torch.Tensor]): Mask tensor for attention
            
        Returns:
            torch.Tensor: Output tensor of shape (batch_size, seq_len, d_model)
        """
        for layer in self.layers:
            x = layer(x, mask)
        return self.layer_norm(x)


class Decoder(nn.Module):
    """
    Decoder stack as described in Section 3.1 of the paper.
    The decoder is composed of a stack of N = 6 identical layers.
    """
    
    def __init__(self, num_layers: int, d_model: int, num_heads: int, d_ff: int, dropout: float = 0.1):
        """
        Initialize the Decoder.
        
        Args:
            num_layers (int): Number of decoder layers (N=6)
            d_model (int): Dimension of the model
            num_heads (int): Number of attention heads
            d_ff (int): Dimension of the feed-forward network
            dropout (float): Dropout rate
        """
        super(Decoder, self).__init__()
        self.layers = nn.ModuleList([
            DecoderLayer(d_model, num_heads, d_ff, dropout) for _ in range(num_layers)
        ])
        self.layer_norm = LayerNorm(d_model)
        
    def forward(self, x: torch.Tensor, 
                encoder_output: torch.Tensor,
                src_mask: Optional[torch.Tensor] = None,
                tgt_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Forward pass through the decoder stack.
        
        Args:
            x (torch.Tensor): Input tensor of shape (batch_size, tgt_seq_len, d_model)
            encoder_output (torch.Tensor): Encoder output of shape (batch_size, src_seq_len, d_model)
            src_mask (Optional[torch.Tensor]): Source mask for encoder-decoder attention
            tgt_mask (Optional[torch.Tensor]): Target mask for self-attention (causal masking)
            
        Returns:
            torch.Tensor: Output tensor of shape (batch_size, tgt_seq_len, d_model)
        """
        for layer in self.layers:
            x = layer(x, encoder_output, src_mask, tgt_mask)
        return self.layer_norm(x)


class Transformer(nn.Module):
    """
    Complete Transformer model as described in the "Attention Is All You Need" paper.
    Implements the encoder-decoder architecture with multi-head attention and position-wise feed-forward networks.
    """
    
    def __init__(self, config, src_vocab_size: int, tgt_vocab_size: int, 
                 src_pad_idx: int = 0, tgt_pad_idx: int = 0):
        """
        Initialize the Transformer model.
        
        Args:
            config: Configuration object containing model hyperparameters
            src_vocab_size (int): Size of the source vocabulary
            tgt_vocab_size (int): Size of the target vocabulary
            src_pad_idx (int): Index of the padding token in source vocabulary
            tgt_pad_idx (int): Index of the padding token in target vocabulary
        """
        super(Transformer, self).__init__()
        
        # Get model configuration
        model_config = config.get_model_config("base")  # Default to base model
        self.d_model = model_config["d_model"]
        self.num_layers = model_config["N"]
        self.num_heads = model_config["h"]
        self.d_ff = model_config["d_ff"]
        self.dropout_rate = model_config["dropout"]
        
        # Padding indices
        self.src_pad_idx = src_pad_idx
        self.tgt_pad_idx = tgt_pad_idx
        
        # Embedding layers
        self.src_embedding = EmbeddingLayer(src_vocab_size, self.d_model, dropout=self.dropout_rate)
        self.tgt_embedding = EmbeddingLayer(tgt_vocab_size, self.d_model, dropout=self.dropout_rate)
        
        # Encoder and Decoder stacks
        self.encoder = Encoder(self.num_layers, self.d_model, self.num_heads, self.d_ff, self.dropout_rate)
        self.decoder = Decoder(self.num_layers, self.d_model, self.num_heads, self.d_ff, self.dropout_rate)
        
        # Output linear layer
        self.output_projection = nn.Linear(self.d_model, tgt_vocab_size)
        
        # Weight sharing between target embedding and output projection as described in Section 3.4
        self.output_projection.weight = self.tgt_embedding.token_embedding.weight
        
        # Initialize parameters with Xavier uniform distribution
        self._init_parameters()
    
    def _init_parameters(self):
        """
        Initialize parameters with Xavier uniform distribution as is common practice.
        """
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)
    
    def _create_padding_mask(self, seq: torch.Tensor, pad_idx: int) -> torch.Tensor:
        """
        Create padding mask to ignore padding tokens in attention.
        
        Args:
            seq (torch.Tensor): Input sequence tensor
            pad_idx (int): Padding token index
            
        Returns:
            torch.Tensor: Padding mask tensor
        """
        # Create mask where pad tokens are True (to be masked)
        mask = (seq == pad_idx)
        # Add dimensions to match attention mask shape: (batch_size, 1, 1, seq_len)
        return mask.unsqueeze(1).unsqueeze(2)
    
    def _create_look_ahead_mask(self, size: int) -> torch.Tensor:
        """
        Create look-ahead mask to prevent attending to future positions.
        
        Args:
            size (int): Size of the sequence
            
        Returns:
            torch.Tensor: Look-ahead mask tensor
        """
        # Create upper triangular matrix with ones (to be masked)
        mask = torch.triu(torch.ones(size, size), diagonal=1).bool()
        # Add dimensions to match attention mask shape: (1, 1, seq_len, seq_len)
        return mask.unsqueeze(0).unsqueeze(1)
    
    def forward(self, src: torch.Tensor, tgt: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through the Transformer model.
        
        Args:
            src (torch.Tensor): Source input tensor of shape (batch_size, src_seq_len)
            tgt (torch.Tensor): Target input tensor of shape (batch_size, tgt_seq_len)
            
        Returns:
            torch.Tensor: Output logits of shape (batch_size, tgt_seq_len, tgt_vocab_size)
        """
        # Create masks
        src_mask = self._create_padding_mask(src, self.src_pad_idx)  # (batch_size, 1, 1, src_seq_len)
        tgt_mask = self._create_padding_mask(tgt, self.tgt_pad_idx)  # (batch_size, 1, 1, tgt_seq_len)
        look_ahead_mask = self._create_look_ahead_mask(tgt.size(1))  # (1, 1, tgt_seq_len, tgt_seq_len)
        # Combine target padding mask with look-ahead mask
        tgt_mask = torch.max(tgt_mask, look_ahead_mask)  # (batch_size, 1, tgt_seq_len, tgt_seq_len)
        
        # Process source through embedding and encoder
        src_embedded = self.src_embedding(src)  # (batch_size, src_seq_len, d_model)
        encoder_output = self.encoder(src_embedded, src_mask)  # (batch_size, src_seq_len, d_model)
        
        # Process target through embedding and decoder
        tgt_embedded = self.tgt_embedding(tgt)  # (batch_size, tgt_seq_len, d_model)
        decoder_output = self.decoder(tgt_embedded, encoder_output, src_mask, tgt_mask)  # (batch_size, tgt_seq_len, d_model)
        
        # Apply output projection
        output = self.output_projection(decoder_output)  # (batch_size, tgt_seq_len, tgt_vocab_size)
        
        return output
    
    def encode(self, src: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Encode the source sequence (used during inference).
        
        Args:
            src (torch.Tensor): Source input tensor of shape (batch_size, src_seq_len)
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor]: Encoder output and source mask
        """
        # Create source mask
        src_mask = self._create_padding_mask(src, self.src_pad_idx)  # (batch_size, 1, 1, src_seq_len)
        
        # Process source through embedding and encoder
        src_embedded = self.src_embedding(src)  # (batch_size, src_seq_len, d_model)
        encoder_output = self.encoder(src_embedded, src_mask)  # (batch_size, src_seq_len, d_model)
        
        return encoder_output, src_mask
    
    def decode(self, tgt: torch.Tensor, encoder_output: torch.Tensor, 
               src_mask: torch.Tensor) -> torch.Tensor:
        """
        Decode the target sequence given encoder output (used during inference).
        
        Args:
            tgt (torch.Tensor): Target input tensor of shape (batch_size, tgt_seq_len)
            encoder_output (torch.Tensor): Encoder output of shape (batch_size, src_seq_len, d_model)
            src_mask (torch.Tensor): Source mask of shape (batch_size, 1, 1, src_seq_len)
            
        Returns:
            torch.Tensor: Output logits of shape (batch_size, tgt_seq_len, tgt_vocab_size)
        """
        # Create target masks
        tgt_mask = self._create_padding_mask(tgt, self.tgt_pad_idx)  # (batch_size, 1, 1, tgt_seq_len)
        look_ahead_mask = self._create_look_ahead_mask(tgt.size(1))  # (1, 1, tgt_seq_len, tgt_seq_len)
        # Combine target padding mask with look-ahead mask
        tgt_mask = torch.max(tgt_mask, look_ahead_mask)  # (batch_size, 1, tgt_seq_len, tgt_seq_len)
        
        # Process target through embedding and decoder
        tgt_embedded = self.tgt_embedding(tgt)  # (batch_size, tgt_seq_len, d_model)
        decoder_output = self.decoder(tgt_embedded, encoder_output, src_mask, tgt_mask)  # (batch_size, tgt_seq_len, d_model)
        
        # Apply output projection
        output = self.output_projection(decoder_output)  # (batch_size, tgt_seq_len, tgt_vocab_size)
        
        return output

