## training/optimizer.py
import torch
import torch.nn as nn
from torch.optim import Adam
import math
from config import config


class CustomOptimizer:
    """
    Custom optimizer implementation that follows the learning rate schedule
    described in the "Attention Is All You Need" paper.
    
    The learning rate schedule is defined as:
    lrate = d_model^(-0.5) * min(step_num^(-0.5), step_num * warmup_steps^(-1.5))
    
    This corresponds to increasing the learning rate linearly for the first warmup_steps
    training steps, and decreasing it thereafter proportionally to the inverse square root
    of the step number.
    """
    
    def __init__(self, model_parameters, d_model: int = None, warmup_steps: int = None):
        """
        Initialize the CustomOptimizer.
        
        Args:
            model_parameters: Iterator over model parameters (e.g., model.parameters())
            d_model (int): Model dimension. If None, will be taken from config.
            warmup_steps (int): Number of warmup steps. If None, will be taken from config.
        """
        # Get configuration values
        training_config = config.get_training_config()
        optimizer_config = config.get_optimizer_config()
        
        # Use provided values or get from config
        self.d_model = d_model or config.get_model_config("base")["d_model"]
        self.warmup_steps = warmup_steps or training_config["warmup_steps"]
        
        # Optimizer hyperparameters from config
        self.beta1 = optimizer_config["beta1"]
        self.beta2 = optimizer_config["beta2"]
        self.epsilon = optimizer_config["epsilon"]
        
        # Initialize step count
        self._step = 0
        
        # Initialize the Adam optimizer
        self.optimizer = Adam(
            model_parameters,
            lr=1.0,  # Initial learning rate, will be adjusted by our schedule
            betas=(self.beta1, self.beta2),
            eps=self.epsilon
        )
        
        # Store initial learning rates to restore them later if needed
        self.initial_lrs = []
        for param_group in self.optimizer.param_groups:
            self.initial_lrs.append(param_group['lr'])
    
    def _get_lr_scale(self) -> float:
        """
        Calculate the learning rate scale based on the current step.
        
        Returns:
            float: Learning rate scale factor
        """
        # Calculate the two components of the learning rate formula
        step_num = self._step
        d_model = self.d_model
        warmup_steps = self.warmup_steps
        
        # Prevent division by zero
        if step_num == 0:
            step_num = 1
            
        # First component: step_num^(-0.5)
        comp1 = step_num ** (-0.5)
        
        # Second component: step_num * warmup_steps^(-1.5)
        comp2 = step_num * (warmup_steps ** (-1.5))
        
        # Return the scale factor: d_model^(-0.5) * min(comp1, comp2)
        return (d_model ** (-0.5)) * min(comp1, comp2)
    
    def _update_learning_rate(self):
        """
        Update the learning rate of the optimizer based on the current step.
        """
        lr_scale = self._get_lr_scale()
        
        # Update learning rate for each parameter group
        for i, param_group in enumerate(self.optimizer.param_groups):
            param_group['lr'] = self.initial_lrs[i] * lr_scale
    
    def step(self):
        """
        Perform a single optimization step.
        """
        self._step += 1
        self._update_learning_rate()
        self.optimizer.step()
    
    def zero_grad(self):
        """
        Clear the gradients of all optimized tensors.
        """
        self.optimizer.zero_grad()
    
    def state_dict(self):
        """
        Return the state of the optimizer.
        """
        return self.optimizer.state_dict()
    
    def load_state_dict(self, state_dict):
        """
        Load the optimizer state.
        
        Args:
            state_dict: Optimizer state dictionary
        """
        self.optimizer.load_state_dict(state_dict)

