## training/trainer.py
import torch
import torch.nn as nn
import torch.nn.functional as F
import pytorch_lightning as pl
from typing import Tuple, Dict, Any, Optional
from model.transformer import Transformer
from training.optimizer import CustomOptimizer
from config import config
import math


class LabelSmoothingLoss(nn.Module):
    """
    Label smoothing implementation as described in Section 7 of the paper.
    This helps prevent the model from becoming overconfident in its predictions.
    """
    
    def __init__(self, vocab_size: int, padding_idx: int, smoothing: float = 0.1):
        """
        Initialize the LabelSmoothingLoss.
        
        Args:
            vocab_size (int): Size of the vocabulary
            padding_idx (int): Index of the padding token
            smoothing (float): Smoothing factor (ϵ_ls = 0.1 as per paper)
        """
        super(LabelSmoothingLoss, self).__init__()
        self.vocab_size = vocab_size
        self.padding_idx = padding_idx
        self.confidence = 1.0 - smoothing
        self.smoothing = smoothing
        
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Compute the label smoothed cross-entropy loss.
        
        Args:
            pred (torch.Tensor): Predictions of shape (batch_size, seq_len, vocab_size)
            target (torch.Tensor): Target labels of shape (batch_size, seq_len)
            
        Returns:
            torch.Tensor: Computed loss
        """
        # Flatten tensors for easier computation
        pred = pred.view(-1, self.vocab_size)
        target = target.view(-1)
        
        # Create distribution with uniform smoothing
        true_dist = torch.zeros_like(pred)
        true_dist.fill_(self.smoothing / (self.vocab_size - 2))  # -2 to exclude target and padding
        true_dist.scatter_(1, target.unsqueeze(1), self.confidence)
        true_dist[:, self.padding_idx] = 0
        
        # Mask out padding tokens
        mask = torch.nonzero(target == self.padding_idx)
        if mask.dim() > 0:
            true_dist.index_fill_(0, mask.squeeze(), 0.0)
            
        # Compute KL divergence
        return F.kl_div(pred.log_softmax(dim=-1), true_dist, reduction='sum')


class TransformerTrainer(pl.LightningModule):
    """
    PyTorch Lightning module for training the Transformer model.
    Manages the training loop, validation, optimization, and logging.
    """
    
    def __init__(self, src_vocab_size: int, tgt_vocab_size: int, 
                 src_pad_idx: int = 0, tgt_pad_idx: int = 0,
                 model_type: str = "base"):
        """
        Initialize the TransformerTrainer.
        
        Args:
            src_vocab_size (int): Size of the source vocabulary
            tgt_vocab_size (int): Size of the target vocabulary
            src_pad_idx (int): Index of the padding token in source vocabulary
            tgt_pad_idx (int): Index of the padding token in target vocabulary
            model_type (str): Type of model ("base" or "big")
        """
        super(TransformerTrainer, self).__init__()
        
        # Save hyperparameters
        self.save_hyperparameters()
        
        # Get configuration
        self.model_config = config.get_model_config(model_type)
        self.training_config = config.get_training_config()
        self.optimizer_config = config.get_optimizer_config()
        
        # Initialize model
        self.model = Transformer(
            config=config,
            src_vocab_size=src_vocab_size,
            tgt_vocab_size=tgt_vocab_size,
            src_pad_idx=src_pad_idx,
            tgt_pad_idx=tgt_pad_idx
        )
        
        # Initialize loss function with label smoothing
        self.criterion = LabelSmoothingLoss(
            vocab_size=tgt_vocab_size,
            padding_idx=tgt_pad_idx,
            smoothing=self.model_config["label_smoothing"]
        )
        
        # Register loss as a metric for logging
        self.train_loss = torch.tensor(0.0)
        self.val_loss = torch.tensor(0.0)
        
    def forward(self, src: torch.Tensor, tgt: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through the model.
        
        Args:
            src (torch.Tensor): Source input tensor
            tgt (torch.Tensor): Target input tensor
            
        Returns:
            torch.Tensor: Model output logits
        """
        return self.model(src, tgt)
    
    def training_step(self, batch: Dict[str, torch.Tensor], batch_idx: int) -> torch.Tensor:
        """
        Perform a single training step.
        
        Args:
            batch (Dict[str, torch.Tensor]): Batch of data containing source/target sequences
            batch_idx (int): Index of the current batch
            
        Returns:
            torch.Tensor: Computed loss for this step
        """
        # Extract batch data
        src_tokens = batch['src_tokens']
        tgt_tokens = batch['tgt_tokens']
        targets = batch['targets']
        
        # Forward pass
        logits = self(src_tokens, tgt_tokens)
        
        # Compute loss
        loss = self.criterion(logits, targets)
        
        # Normalize loss by number of non-padding tokens
        non_padding_tokens = (targets != self.hparams.tgt_pad_idx).sum().float()
        if non_padding_tokens > 0:
            loss = loss / non_padding_tokens
            
        # Log training loss
        self.log('train_loss', loss, on_step=True, on_epoch=True, prog_bar=True, logger=True)
        
        # Log learning rate
        lr = self.optimizers().param_groups[0]['lr']
        self.log('learning_rate', lr, on_step=True, on_epoch=False, prog_bar=False, logger=True)
        
        return loss
    
    def validation_step(self, batch: Dict[str, torch.Tensor], batch_idx: int) -> torch.Tensor:
        """
        Perform a single validation step.
        
        Args:
            batch (Dict[str, torch.Tensor]): Batch of data containing source/target sequences
            batch_idx (int): Index of the current batch
            
        Returns:
            torch.Tensor: Computed loss for this step
        """
        # Extract batch data
        src_tokens = batch['src_tokens']
        tgt_tokens = batch['tgt_tokens']
        targets = batch['targets']
        
        # Forward pass
        logits = self(src_tokens, tgt_tokens)
        
        # Compute loss
        loss = self.criterion(logits, targets)
        
        # Normalize loss by number of non-padding tokens
        non_padding_tokens = (targets != self.hparams.tgt_pad_idx).sum().float()
        if non_padding_tokens > 0:
            loss = loss / non_padding_tokens
            
        # Log validation loss
        self.log('val_loss', loss, on_step=False, on_epoch=True, prog_bar=True, logger=True)
        
        return loss
    
    def configure_optimizers(self):
        """
        Configure the optimizer and learning rate scheduler.
        
        Returns:
            Dict: Dictionary containing optimizer and scheduler configuration
        """
        # Initialize custom optimizer with model parameters
        optimizer = CustomOptimizer(
            model_parameters=self.parameters(),
            d_model=self.model_config["d_model"],
            warmup_steps=self.training_config["warmup_steps"]
        )
        
        # Return optimizer (scheduler is handled internally by CustomOptimizer)
        return optimizer
    
    def train_dataloader(self):
        """
        This method would typically return a DataLoader, but in our implementation,
        the DataLoader is created outside and passed to the PyTorch Lightning Trainer.
        This is just a placeholder to satisfy the LightningModule interface.
        """
        pass
    
    def val_dataloader(self):
        """
        This method would typically return a DataLoader, but in our implementation,
        the DataLoader is created outside and passed to the PyTorch Lightning Trainer.
        This is just a placeholder to satisfy the LightningModule interface.
        """
        pass

