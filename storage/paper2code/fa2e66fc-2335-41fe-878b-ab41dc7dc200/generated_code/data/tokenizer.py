## data/tokenizer.py
import os
from typing import List, Union
from tokenizers import Tokenizer
from tokenizers.models import BPE, WordPiece
from tokenizers.trainers import <PERSON><PERSON><PERSON><PERSON><PERSON>, WordPieceTrainer
from tokenizers.pre_tokenizers import Whitespace
from tokenizers.processors import TemplateProcessing


class TokenizerWrapper:
    """
    Wrapper class for HuggingFace tokenizers to handle both BPE and WordPiece tokenization.
    Used for encoding text to token IDs and decoding back to text.
    """
    
    def __init__(self, vocab_path: str = None, vocab_type: str = "byte-pair", vocab_size: int = 37000):
        """
        Initialize the tokenizer based on vocabulary type.
        
        Args:
            vocab_path (str): Path to the vocabulary file or directory
            vocab_type (str): Type of vocabulary - "byte-pair" or "word-piece"
            vocab_size (int): Size of the vocabulary
        """
        self.vocab_type = vocab_type
        self.vocab_size = vocab_size
        
        if vocab_type == "byte-pair":
            # Initialize BPE tokenizer
            self.tokenizer = Tokenizer(BPE(unk_token="[UNK]"))
            self.tokenizer.pre_tokenizer = Whitespace()
            self.tokenizer.post_processor = TemplateProcessing(
                single="[BOS] $A [EOS]",
                pair="[BOS] $A [EOS] $B:1 [EOS]:1",
                special_tokens=[
                    ("[BOS]", 1),
                    ("[EOS]", 2),
                    ("[UNK]", 3),
                ],
            )
        elif vocab_type == "word-piece":
            # Initialize WordPiece tokenizer
            self.tokenizer = Tokenizer(WordPiece(unk_token="[UNK]"))
            self.tokenizer.pre_tokenizer = Whitespace()
            self.tokenizer.post_processor = TemplateProcessing(
                single="[BOS] $A [EOS]",
                pair="[BOS] $A [EOS] $B:1 [EOS]:1",
                special_tokens=[
                    ("[BOS]", 1),
                    ("[EOS]", 2),
                    ("[UNK]", 3),
                ],
            )
        else:
            raise ValueError("vocab_type must be either 'byte-pair' or 'word-piece'")
        
        # Load vocabulary if path is provided and exists
        if vocab_path and os.path.exists(vocab_path):
            self.tokenizer.load(vocab_path)
    
    def train(self, files: Union[str, List[str]]) -> None:
        """
        Train the tokenizer on the given files.
        
        Args:
            files (Union[str, List[str]]): Path(s) to the training files
        """
        if self.vocab_type == "byte-pair":
            trainer = BpeTrainer(
                vocab_size=self.vocab_size,
                special_tokens=["[PAD]", "[BOS]", "[EOS]", "[UNK]"],
                show_progress=True
            )
        else:  # word-piece
            trainer = WordPieceTrainer(
                vocab_size=self.vocab_size,
                special_tokens=["[PAD]", "[BOS]", "[EOS]", "[UNK]"],
                show_progress=True
            )
        
        if isinstance(files, str):
            files = [files]
        
        self.tokenizer.train(files, trainer)
    
    def encode(self, text: str) -> List[int]:
        """
        Convert input text to list of token IDs.
        
        Args:
            text (str): Input text to encode
            
        Returns:
            List[int]: List of token IDs
        """
        return self.tokenizer.encode(text).ids
    
    def decode(self, ids: List[int]) -> str:
        """
        Convert list of token IDs back to text.
        
        Args:
            ids (List[int]): List of token IDs to decode
            
        Returns:
            str: Decoded text
        """
        return self.tokenizer.decode(ids)
    
    def get_vocab_size(self) -> int:
        """
        Get the size of the vocabulary.
        
        Returns:
            int: Size of the vocabulary
        """
        return self.tokenizer.get_vocab_size()
    
    def save_vocab(self, path: str) -> None:
        """
        Save the vocabulary to the specified path.
        
        Args:
            path (str): Path to save the vocabulary
        """
        self.tokenizer.save(path)
    
    def get_vocab(self) -> dict:
        """
        Get the vocabulary as a dictionary.
        
        Returns:
            dict: Vocabulary dictionary
        """
        return self.tokenizer.get_vocab()

