## config.py
import yaml
from typing import Dict, Any, Union


class Config:
    """
    Configuration management class for the Transformer model.
    Loads and provides access to all hyperparameters and model settings
    defined in the config.yaml file.
    """
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        Initialize the configuration by loading from a YAML file.
        
        Args:
            config_path (str): Path to the configuration YAML file
        """
        with open(config_path, 'r') as file:
            self._config = yaml.safe_load(file)
    
    def get_model_config(self, model_type: str = "base") -> Dict[str, Any]:
        """
        Get model configuration for either base or big model.
        
        Args:
            model_type (str): Either "base" or "big"
            
        Returns:
            Dict containing model configuration parameters
        """
        if model_type not in ["base", "big"]:
            raise ValueError("model_type must be either 'base' or 'big'")
        
        return self._config["model"][model_type]
    
    def get_training_config(self) -> Dict[str, Any]:
        """
        Get training configuration parameters.
        
        Returns:
            Dict containing training configuration parameters
        """
        return self._config["training"]
    
    def get_optimizer_config(self) -> Dict[str, Any]:
        """
        Get optimizer configuration parameters.
        
        Returns:
            Dict containing optimizer configuration parameters
        """
        return self._config["optimizer"]
    
    def get_inference_config(self) -> Dict[str, Any]:
        """
        Get inference configuration parameters.
        
        Returns:
            Dict containing inference configuration parameters
        """
        return self._config["inference"]
    
    def get_dataset_config(self, dataset_type: str) -> Dict[str, Any]:
        """
        Get dataset configuration for either English-German or English-French.
        
        Args:
            dataset_type (str): Either "en_de" or "en_fr"
            
        Returns:
            Dict containing dataset configuration parameters
        """
        if dataset_type not in ["en_de", "en_fr"]:
            raise ValueError("dataset_type must be either 'en_de' or 'en_fr'")
        
        return self._config["dataset"][dataset_type]
    
    def get_hardware_config(self) -> Dict[str, Any]:
        """
        Get hardware configuration parameters.
        
        Returns:
            Dict containing hardware configuration parameters
        """
        return self._config["hardware"]
    
    def get_evaluation_config(self) -> Dict[str, Any]:
        """
        Get evaluation configuration parameters.
        
        Returns:
            Dict containing evaluation configuration parameters
        """
        return self._config["evaluation"]
    
    def get_all_config(self) -> Dict[str, Any]:
        """
        Get the entire configuration as a dictionary.
        
        Returns:
            Dict containing all configuration parameters
        """
        return self._config


# Global configuration instance
config = Config()
