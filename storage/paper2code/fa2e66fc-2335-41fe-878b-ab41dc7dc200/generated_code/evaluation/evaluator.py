## evaluation/evaluator.py
import torch
import torch.nn.functional as F
import sacrebleu
from typing import List, Dict, Tuple, Optional
from model.transformer import Transformer
from data.dataset import TranslationDataset
from data.tokenizer import TokenizerWrapper
from config import Config
import math
from tqdm import tqdm


class Evaluator:
    """
    Evaluator class for the Transformer model.
    Performs model evaluation and BLEU scoring.
    Implements beam search decoding for translation generation.
    """
    
    def __init__(self, model: Transformer, test_dataset: TranslationDataset, 
                 tokenizer: TokenizerWrapper, config: Config):
        """
        Initialize the Evaluator.
        
        Args:
            model (Transformer): Trained Transformer model
            test_dataset (TranslationDataset): Test dataset for evaluation
            tokenizer (TokenizerWrapper): Tokenizer for encoding/decoding
            config (Config): Configuration object
        """
        self.model = model
        self.test_dataset = test_dataset
        self.tokenizer = tokenizer
        self.config = config
        
        # Get inference configuration
        self.inference_config = config.get_inference_config()
        self.beam_size = self.inference_config["beam_size"]
        self.length_penalty = self.inference_config["length_penalty"]
        self.max_output_length = self.inference_config["max_output_length"]
        
        # Get evaluation configuration
        self.eval_config = config.get_evaluation_config()
        
        # Set device
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model.to(self.device)
        
        # Put model in evaluation mode
        self.model.eval()
    
    def _beam_search(self, src: torch.Tensor, src_mask: torch.Tensor) -> List[int]:
        """
        Perform beam search decoding for a single source sequence.
        
        Args:
            src (torch.Tensor): Source sequence tensor of shape (1, src_seq_len)
            src_mask (torch.Tensor): Source mask tensor
            
        Returns:
            List[int]: Decoded token IDs
        """
        # Get batch size and source sequence length
        batch_size, src_seq_len = src.size()
        
        # Initialize beam with start token (assumed to be 1)
        start_token = 1
        end_token = 2
        
        # Initialize beam: each element is (sequence, score)
        beam = [([start_token], 0.0)]
        
        # Completed sequences
        completed = []
        
        # Maximum output length
        max_len = src_seq_len + self.max_output_length
        
        # Decode step by step
        for _ in range(max_len):
            candidates = []
            
            # Expand each sequence in the beam
            for seq, score in beam:
                # Convert sequence to tensor
                tgt_tensor = torch.tensor([seq], dtype=torch.long, device=self.device)
                
                # Decode using the model
                with torch.no_grad():
                    logits = self.model.decode(tgt_tensor, src, src_mask)
                
                # Get probabilities for the last token
                probs = F.log_softmax(logits[0, -1, :], dim=-1)
                
                # Get top-k candidates
                top_k_probs, top_k_indices = torch.topk(probs, self.beam_size)
                
                # Add candidates to list
                for i in range(self.beam_size):
                    token_id = top_k_indices[i].item()
                    token_score = top_k_probs[i].item()
                    
                    # Apply length penalty
                    length = len(seq) - 1  # Exclude start token
                    penalized_score = (score + token_score) / (length ** self.length_penalty)
                    
                    new_seq = seq + [token_id]
                    candidates.append((new_seq, score + token_score, penalized_score))
            
            # Sort candidates by penalized score and keep top-k
            candidates.sort(key=lambda x: x[2], reverse=True)
            candidates = candidates[:self.beam_size]
            
            # Separate completed and active sequences
            beam = []
            for seq, score, penalized_score in candidates:
                if seq[-1] == end_token:
                    completed.append((seq, score, penalized_score))
                else:
                    beam.append((seq, score))
            
            # If beam is empty, break
            if not beam:
                break
        
        # If no completed sequences, use the best active sequence
        if not completed:
            completed = [(seq, score, score / (len(seq) - 1) ** self.length_penalty) for seq, score in beam]
        
        # Sort completed sequences by penalized score
        completed.sort(key=lambda x: x[2], reverse=True)
        
        # Return the best sequence (excluding start token)
        best_seq = completed[0][0][1:] if completed else []
        
        # Remove end token if present
        if end_token in best_seq:
            end_idx = best_seq.index(end_token)
            best_seq = best_seq[:end_idx]
        
        return best_seq
    
    def translate_sentence(self, sentence: str) -> str:
        """
        Translate a single sentence.
        
        Args:
            sentence (str): Input sentence to translate
            
        Returns:
            str: Translated sentence
        """
        # Encode input sentence
        src_tokens = self.tokenizer.encode(sentence)
        src_tensor = torch.tensor([src_tokens], dtype=torch.long, device=self.device)
        
        # Encode source sequence
        with torch.no_grad():
            encoder_output, src_mask = self.model.encode(src_tensor)
        
        # Perform beam search
        tgt_tokens = self._beam_search(encoder_output, src_mask)
        
        # Decode output tokens
        translation = self.tokenizer.decode(tgt_tokens)
        
        return translation
    
    def evaluate(self) -> Dict[str, float]:
        """
        Evaluate the model on the test dataset and compute BLEU score.
        
        Returns:
            Dict[str, float]: Dictionary containing evaluation metrics
        """
        # Lists to store references and hypotheses
        references = []
        hypotheses = []
        
        # Process test dataset
        for i in tqdm(range(len(self.test_dataset)), desc="Evaluating"):
            # Get batch
            batch = self.test_dataset[i]
            src_batch = batch['source_tokens']
            tgt_batch = batch['target_tokens']
            
            # Process each sequence in the batch
            for j in range(len(src_batch)):
                # Get source and target sequences
                src_tokens = src_batch[j]
                tgt_tokens = tgt_batch[j]
                
                # Convert to tensors
                src_tensor = torch.tensor([src_tokens], dtype=torch.long, device=self.device)
                
                # Encode source sequence
                with torch.no_grad():
                    encoder_output, src_mask = self.model.encode(src_tensor)
                
                # Perform beam search
                pred_tokens = self._beam_search(encoder_output, src_mask)
                
                # Decode tokens to text
                reference = self.tokenizer.decode(tgt_tokens[1:-1])  # Remove BOS and EOS tokens
                hypothesis = self.tokenizer.decode(pred_tokens)
                
                # Add to lists
                references.append([reference])  # SacreBLEU expects list of references
                hypotheses.append(hypothesis)
        
        # Compute BLEU score
        bleu = sacrebleu.corpus_bleu(hypotheses, references)
        
        # Return metrics
        return {
            "bleu_score": bleu.score,
            "bleu_details": {
                "1-gram": bleu.precisions[0],
                "2-gram": bleu.precisions[1],
                "3-gram": bleu.precisions[2],
                "4-gram": bleu.precisions[3],
                "brevity_penalty": bleu.bp,
                "length_ratio": bleu.sys_len / bleu.ref_len
            }
        }

