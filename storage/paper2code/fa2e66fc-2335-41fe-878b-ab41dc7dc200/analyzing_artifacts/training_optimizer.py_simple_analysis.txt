### Logic Analysis for training/optimizer.py

Based on the paper, plan, design, and configuration, the optimizer module needs to implement the custom learning rate schedule as described in equation (3) of the paper:

```
lrate = d_model^(-0.5) * min(step_num^(-0.5), step_num * warmup_steps^(-1.5))
```

This corresponds to increasing the learning rate linearly for the first `warmup_steps` training steps, and decreasing it thereafter proportionally to the inverse square root of the step number.

#### Key Requirements:
1. Implement the custom learning rate schedule that varies with training steps
2. Wrap PyTorch Adam optimizer with the varying learning rate
3. Use d_model and warmup_steps from config.yaml
4. Support the specific hyperparameters from the paper:
   - β₁ = 0.9
   - β₂ = 0.98
   - ϵ = 10⁻⁹
   - warmup_steps = 4000

#### Implementation Logic:
1. Create a custom learning rate scheduler that computes the learning rate based on the current step number
2. Initialize the PyTorch Adam optimizer with the specified hyperparameters
3. Override the optimizer's step function to update the learning rate before each optimization step
4. The learning rate calculation follows the formula from the paper where:
   - For steps < warmup_steps: learning rate increases linearly
   - For steps >= warmup_steps: learning rate decreases with inverse square root of step number

#### Dependencies:
1. Config module for accessing d_model and warmup_steps values
2. PyTorch for the underlying Adam optimizer implementation

#### Interface Requirements:
1. Constructor that accepts model parameters, d_model, and warmup_steps
2. Standard optimizer methods (step, zero_grad)
3. Internal method for computing the learning rate based on current step

This implementation will ensure that the optimizer follows the exact learning rate schedule described in the paper, which is critical for reproducing the Transformer model's training dynamics.