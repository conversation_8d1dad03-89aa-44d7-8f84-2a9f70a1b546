# DOCX处理功能设置指南

## 概述

DOCX处理功能通过以下步骤工作：
1. 将DOCX文件转换为PDF格式
2. 使用现有的PDF处理API将PDF转换为JSON
3. 如果转换失败，使用备选方案直接解析DOCX内容

## 系统依赖安装

### Windows系统

1. **安装Pandoc**
   ```bash
   # 使用Chocolatey安装
   choco install pandoc
   
   # 或者从官网下载安装包
   # https://pandoc.org/installing.html
   ```

2. **安装LaTeX (推荐MiKTeX)**
   ```bash
   # 使用Chocolatey安装
   choco install miktex
   
   # 或者从官网下载安装包
   # https://miktex.org/download
   ```

### Linux系统

1. **安装Pandoc**
   ```bash
   # Ubuntu/Debian
   sudo apt-get install pandoc
   
   # CentOS/RHEL
   sudo yum install pandoc
   ```

2. **安装LaTeX**
   ```bash
   # Ubuntu/Debian
   sudo apt-get install texlive-xetex texlive-fonts-recommended texlive-plain-generic
   
   # CentOS/RHEL
   sudo yum install texlive-xetex texlive-collection-fontsrecommended
   ```

### macOS系统

1. **安装Pandoc**
   ```bash
   # 使用Homebrew
   brew install pandoc
   ```

2. **安装LaTeX**
   ```bash
   # 使用Homebrew安装BasicTeX
   brew install --cask basictex
   
   # 或者安装完整的MacTeX
   brew install --cask mactex
   ```

## 验证安装

安装完成后，可以通过以下命令验证：

```bash
# 验证Pandoc
pandoc --version

# 验证XeLaTeX
xelatex --version
```

## 功能特性

1. **主要转换流程**：DOCX → PDF → JSON
2. **备选方案**：如果PDF转换失败，直接解析DOCX内容
3. **错误处理**：多层次的错误处理和回退机制
4. **中文支持**：使用XeLaTeX引擎确保中文字符正确处理

## 注意事项

1. 首次使用时，LaTeX可能需要下载额外的包，这可能需要一些时间
2. 确保系统有足够的磁盘空间用于临时文件
3. 复杂的DOCX文档（包含大量图片、表格）转换时间可能较长
4. 如果遇到转换问题，系统会自动使用备选方案直接解析DOCX内容
